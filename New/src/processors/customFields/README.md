# Custom Field Processing System

Comprehensive custom field processing utilities for AutoPatient (AP) and CliniCore (CC) platforms with intelligent field synchronization, bidirectional value conversion, and production-ready performance.

## Overview

This module provides a complete custom field processing system with:

- **Field Type Mapping**: Comprehensive bidirectional field type mapping with special handling
- **Intelligent Field Matching**: Multi-strategy field matching with normalization
- **Value Conversion**: Bidirectional value conversion with type-specific handling
- **Field Creation**: Smart field creation with conflict detection and resolution
- **Synchronization**: Complete field synchronization with database operations

## 🆕 Updated Field Mapping System

**Key Requirements Implemented:**
- ✅ **Unidirectional Field Creation**: Only creates custom fields in AP, never in CC
- ✅ **Silent Field Skipping**: Missing CC fields are skipped without noise during AP→CC sync
- ✅ **Intelligent Type Compatibility**: Handles PHONE/telephone, TEXT/textarea mismatches gracefully
- ✅ **FILE_UPLOAD Skipping**: FILE_UPLOAD fields are skipped entirely during sync
- ✅ **Enhanced Field Matching**: Uses name, label, fieldKey properties with normalization

## Key Features

- ✅ **Bidirectional Conversion**: Reversible transformations preserve data integrity
- ✅ **Enhanced Boolean Detection**: Any 2-option field automatically converts to boolean type
- ✅ **Dynamic Option Synchronization**: Handles option updates, additions, and removals
- ✅ **Automatic Request ID**: Uses `getRequestId()` for seamless logging integration
- ✅ **Comprehensive Logging**: Detailed logging with automatic request ID tracing
- ✅ **Graceful Fallbacks**: Unmappable types convert to text fields with warnings
- ✅ **Strict TypeScript**: No `any` usage, full type safety
- ✅ **Performance Optimized**: Pure functions suitable for bulk operations

## Field Type Conversion Matrix

### AutoPatient → CliniCore

| AP Field Type | AP Options | CC Field Type | CC Properties |
|---------------|------------|---------------|---------------|
| `RADIO` | **Exactly 2 options (any content)** | `boolean` | - |
| `RADIO` | >2 options or 0-1 options | `select` | `allowMultipleValues: false` |
| `MULTIPLE_OPTIONS` | Any options | `select` | `allowMultipleValues: true` |
| `SINGLE_OPTIONS` | Any options | `select` | `allowMultipleValues: false` |
| `TEXT` | - | `text` | - |
| *Other types* | - | `text` | *Fallback conversion* |

### CliniCore → AutoPatient

| CC Field Type | CC Properties | AP Field Type | AP Properties |
|---------------|---------------|---------------|---------------|
| `boolean` | - | `RADIO` | `options: ["Yes", "No"]` |
| `select-or-custom` | Any values | `SINGLE_OPTIONS` | Preserves allowed values |
| `select` | `allowMultipleValues: true` | `MULTIPLE_OPTIONS` | Preserves allowed values |
| `select` | `allowMultipleValues: false` | `SINGLE_OPTIONS` | Preserves allowed values |
| `number` | - | `NUMERICAL` | - |
| `textarea` | - | `LARGE_TEXT` | - |
| `text`, `email`, `telephone` | - | `TEXT` | - |
| *Other types* | - | `TEXT` | *Fallback conversion* |

## Usage Examples

### Basic Conversion

```typescript
import { apToCcCustomFieldConvert, ccToApCustomFieldConvert } from '@processors/customFields';

// Convert AP 2-option field to CC boolean (any 2 options = boolean)
const apBooleanField: APGetCustomFieldType = {
  id: "field123",
  name: "Terms Agreement",
  dataType: "RADIO",
  picklistOptions: ["Accept", "Decline"]  // Any 2 options convert to boolean
};

const ccField = apToCcCustomFieldConvert(apBooleanField);
// Result: { name: "Terms Agreement", type: "boolean", ... }

// Convert CC select field to AP
const ccSelectField: GetCCCustomField = {
  id: 1,
  name: "interests",
  label: "User Interests",
  type: "select",
  allowMultipleValues: true,
  allowedValues: [
    { id: 1, value: "Sports" },
    { id: 2, value: "Music" }
  ]
};

const apField = ccToApCustomFieldConvert(ccSelectField);
// Result: { name: "User Interests", dataType: "MULTIPLE_OPTIONS", options: ["Sports", "Music"] }
```

### Bidirectional Conversion

```typescript
// Test reversibility with enhanced 2-option detection
const originalAp: APGetCustomFieldType = {
  id: "test123",
  name: "User Preference",
  dataType: "RADIO",
  picklistOptions: ["Enabled", "Disabled"]  // Any 2 options work
};

// AP → CC → AP conversion
const convertedCc = apToCcCustomFieldConvert(originalAp);
const backToAp = ccToApCustomFieldConvert(convertedCc);

// backToAp should maintain compatibility with originalAp
console.log(backToAp.dataType); // "RADIO"
console.log(backToAp.options); // ["Yes", "No"] (standardized for boolean compatibility)
```

### Error Handling & Dynamic Options

```typescript
// Unmappable field types are handled gracefully
const unknownApField: APGetCustomFieldType = {
  id: "unknown123",
  name: "Unknown Field",
  dataType: "CUSTOM_TYPE" // Not a standard type
};

const fallbackCc = apToCcCustomFieldConvert(unknownApField);
// Result: { type: "text", ... } with warning logged

// Dynamic option detection
const dynamicField: APGetCustomFieldType = {
  id: "dynamic123",
  name: "Project Status",
  dataType: "SINGLE_OPTIONS",
  picklistOptions: ["v1.0", "v2.0", "v3.0-beta"] // Versioned options detected
};

const ccDynamicField = apToCcCustomFieldConvert(dynamicField);
// Logs: "Dynamic option pattern detected" with monitoring recommendations
```

## Logging

All conversions include comprehensive logging with automatic request ID detection:

- **DEBUG Level**: Detailed conversion steps, field mappings, option transformations, dynamic option detection
- **WARN Level**: Fallback conversions, unmappable field types, potential data loss, large option sets

Example log output:
```
[auto-req-456] DEBUG Custom Field AP→CC conversion started: Terms Agreement { apFieldType: "RADIO", hasOptions: true, optionCount: 2 }
[auto-req-456] DEBUG Custom Field AP→CC RADIO→boolean conversion: Terms Agreement { detectedAsBoolean: true, originalOptions: ["Accept", "Decline"], conversionReason: "exactly_2_options" }
[auto-req-456] DEBUG Custom Field Dynamic option pattern detected: Project Status { hasVersionedOptions: true, optionCount: 3, recommendation: "monitor_for_option_updates" }
```

## Enhanced Boolean Detection

The system now detects **any 2-option field** as a boolean candidate, regardless of option content:

- **2-Option Examples**: ["Accept", "Decline"], ["Enabled", "Disabled"], ["True", "False"], ["Option A", "Option B"]
- **Conversion Rule**: Exactly 2 options = boolean field type
- **Reversibility**: CC boolean fields convert back to AP RADIO with standardized ["Yes", "No"] options

This broader approach supports various binary choices beyond traditional Yes/No patterns.

## Dynamic Option Synchronization

The system detects and handles dynamic option updates:

### Detection Patterns
- **Numbered Options**: "1. First", "2. Second"
- **Versioned Options**: "v1.0", "v2.0", "version 3"
- **Date Options**: "2024", "01/15", "Q1 2024"
- **Status Options**: "active", "inactive", "pending", "draft"
- **Empty Options**: Fields with no current options

### Synchronization Features
- **Option Addition**: Handles new options added to existing fields
- **Option Removal**: Graceful handling of removed options
- **Option Modification**: Detects when option values change
- **Large Option Sets**: Special monitoring for fields with >20 options
- **Custom Options**: Support for user-added options in select-or-custom fields

## Performance Considerations

- **Pure Functions**: No side effects, safe for concurrent use
- **Minimal Memory**: Efficient object creation and array operations
- **Early Returns**: Optimized execution paths for common cases
- **Bulk Operations**: Suitable for processing large numbers of fields

## Type Safety

All functions use strict TypeScript typing:
- No `any` or `as any` usage
- Full type inference and checking
- Database schema inferred types where applicable
- Comprehensive JSDoc documentation
