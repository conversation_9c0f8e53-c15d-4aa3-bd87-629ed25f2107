/**
 * AutoPatient Value Conversion Functions
 *
 * Provides value conversion utilities for transforming AutoPatient field values
 * to CliniCore format. Handles type-specific conversions, multi-value fields,
 * and special cases like boolean to radio conversions.
 *
 * @fileoverview AP → CC value conversion functions
 * @version 1.0.0
 * @since 2024-07-28
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@/type";
import type { ValueConversionResult } from "../types";
import { getApToCcMapping, isRadioBooleanCandidate } from "../fieldTypeMapping";
import { logDebug, logWarn } from "@/utils/logger";

/**
 * Convert AutoPatient field value to CliniCore format
 *
 * Main entry point for AP → CC value conversion. Handles all supported
 * field type conversions with proper error handling and logging.
 *
 * @param value - AP field value to convert
 * @param apField - Source AP field definition
 * @param ccField - Target CC field definition
 * @param requestId - Request ID for tracing
 * @returns Conversion result with success status and converted value
 */
export async function convertApValueToCc(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string
): Promise<ValueConversionResult> {
	// Handle null/undefined values
	if (value === null || value === undefined) {
		logDebug("Converting null/undefined AP value", {
			requestId,
			apFieldId: apField.id,
			ccFieldId: ccField.id,
		});
		return { success: true, convertedValue: null };
	}

	try {
		// Get field type mapping
		const mapping = getApToCcMapping(apField.dataType, apField);
		if (!mapping) {
			return {
				success: false,
				convertedValue: null,
				error: `AP field type ${apField.dataType} should be skipped (FILE_UPLOAD)`
			};
		}

		// Handle specific conversion cases
		switch (apField.dataType) {
			case "RADIO":
				return convertApRadioToCc(value, apField, ccField, requestId);
			
			case "MULTIPLE_OPTIONS":
				return convertApMultipleOptionsToCc(value, apField, ccField, requestId);
			
			case "SINGLE_OPTIONS":
				return convertApSingleOptionsToCc(value, apField, ccField, requestId);
			
			case "TEXTBOX_LIST":
				return convertApTextboxListToCc(value, apField, ccField, requestId);
			
			case "CHECKBOX":
				return convertApCheckboxToCc(value, apField, ccField, requestId);
			
			case "TEXT":
			case "LARGE_TEXT":
			case "EMAIL":
			case "PHONE":
			case "MONETORY":
				return convertApTextToCc(value, apField, ccField, requestId);
			
			case "NUMERICAL":
				return convertApNumericalToCc(value, apField, ccField, requestId);
			
			case "DATE":
				return convertApDateToCc(value, apField, ccField, requestId);
			
			default:
				return {
					success: false,
					convertedValue: null,
					error: `Unsupported AP field type: ${apField.dataType}`
				};
		}
	} catch (error) {
		logWarn("Error converting AP value to CC", {
			requestId,
			apFieldId: apField.id,
			ccFieldId: ccField.id,
			error: error instanceof Error ? error.message : String(error)
		});

		return {
			success: false,
			convertedValue: null,
			error: `Conversion error: ${error instanceof Error ? error.message : String(error)}`
		};
	}
}

/**
 * Convert AP RADIO field value to CC format
 */
function convertApRadioToCc(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string
): ValueConversionResult {
	const stringValue = String(value);

	// Check if this RADIO field should be converted to boolean
	if (ccField.type === "boolean" && isRadioBooleanCandidate(apField)) {
		return convertApRadioToBoolean(stringValue, apField, requestId);
	}

	// Convert to CC select field
	if (ccField.type === "select") {
		// Find matching allowed value in CC field
		const matchingValue = ccField.allowedValues?.find(av => 
			av.value.toLowerCase() === stringValue.toLowerCase()
		);

		if (matchingValue) {
			return { success: true, convertedValue: matchingValue.value };
		}

		// If no exact match, use the original value
		logWarn("No matching CC select value for AP radio option", {
			requestId,
			apValue: stringValue,
			ccAllowedValues: ccField.allowedValues?.map(av => av.value)
		});

		return { 
			success: true, 
			convertedValue: stringValue,
			warnings: [`No exact match found for radio value: ${stringValue}`]
		};
	}

	return {
		success: false,
		convertedValue: null,
		error: `Cannot convert AP RADIO to CC ${ccField.type}`
	};
}

/**
 * Convert AP RADIO value to CC boolean
 */
function convertApRadioToBoolean(
	value: string,
	apField: APGetCustomFieldType,
	requestId: string
): ValueConversionResult {
	const normalizedValue = value.toLowerCase().trim();
	
	// Yes patterns
	const yesPatterns = ["yes", "ja", "true", "1", "on", "enabled"];
	// No patterns  
	const noPatterns = ["no", "nein", "false", "0", "off", "disabled"];

	if (yesPatterns.includes(normalizedValue)) {
		return { success: true, convertedValue: true };
	}

	if (noPatterns.includes(normalizedValue)) {
		return { success: true, convertedValue: false };
	}

	// Default to false for unrecognized values
	logWarn("Unrecognized radio value for boolean conversion, defaulting to false", {
		requestId,
		apFieldId: apField.id,
		value: value,
		normalizedValue
	});

	return { 
		success: true, 
		convertedValue: false,
		warnings: [`Unrecognized radio value "${value}" converted to false`]
	};
}

/**
 * Convert AP MULTIPLE_OPTIONS to CC select (multi-value)
 */
function convertApMultipleOptionsToCc(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string
): ValueConversionResult {
	if (ccField.type !== "select" || !ccField.allowMultipleValues) {
		return {
			success: false,
			convertedValue: null,
			error: `Cannot convert AP MULTIPLE_OPTIONS to CC ${ccField.type} (allowMultipleValues: ${ccField.allowMultipleValues})`
		};
	}

	// Handle array values
	let values: string[];
	if (Array.isArray(value)) {
		values = value.map(v => String(v));
	} else if (typeof value === "string") {
		// Split comma-separated values
		values = value.split(",").map(v => v.trim()).filter(v => v.length > 0);
	} else {
		values = [String(value)];
	}

	// Map to CC allowed values
	const convertedValues: string[] = [];
	const warnings: string[] = [];

	for (const val of values) {
		const matchingValue = ccField.allowedValues?.find(av => 
			av.value.toLowerCase() === val.toLowerCase()
		);

		if (matchingValue) {
			convertedValues.push(matchingValue.value);
		} else {
			convertedValues.push(val);
			warnings.push(`No exact match found for option: ${val}`);
		}
	}

	return { 
		success: true, 
		convertedValue: convertedValues,
		warnings: warnings.length > 0 ? warnings : undefined
	};
}

/**
 * Convert AP SINGLE_OPTIONS to CC select (single value)
 */
function convertApSingleOptionsToCc(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string
): ValueConversionResult {
	if (ccField.type !== "select" || ccField.allowMultipleValues) {
		return {
			success: false,
			convertedValue: null,
			error: `Cannot convert AP SINGLE_OPTIONS to CC ${ccField.type} (allowMultipleValues: ${ccField.allowMultipleValues})`
		};
	}

	const stringValue = Array.isArray(value) ? value[0] : String(value);

	// Find matching allowed value in CC field
	const matchingValue = ccField.allowedValues?.find(av => 
		av.value.toLowerCase() === stringValue.toLowerCase()
	);

	if (matchingValue) {
		return { success: true, convertedValue: matchingValue.value };
	}

	// Use original value if no exact match
	return { 
		success: true, 
		convertedValue: stringValue,
		warnings: [`No exact match found for option: ${stringValue}`]
	};
}

/**
 * Convert AP TEXTBOX_LIST to CC multi-value field
 */
function convertApTextboxListToCc(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string
): ValueConversionResult {
	if (!ccField.allowMultipleValues) {
		return {
			success: false,
			convertedValue: null,
			error: `Cannot convert AP TEXTBOX_LIST to CC ${ccField.type} (allowMultipleValues: false)`
		};
	}

	// Handle array values
	let values: string[];
	if (Array.isArray(value)) {
		values = value.map(v => String(v));
	} else if (typeof value === "string") {
		// Split by newlines or commas
		values = value.split(/[\n,]/).map(v => v.trim()).filter(v => v.length > 0);
	} else {
		values = [String(value)];
	}

	return { success: true, convertedValue: values };
}

/**
 * Convert AP CHECKBOX to CC select (multi-value)
 */
function convertApCheckboxToCc(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string
): ValueConversionResult {
	// Similar to MULTIPLE_OPTIONS conversion
	return convertApMultipleOptionsToCc(value, apField, ccField, requestId);
}

/**
 * Convert AP text-based fields to CC
 */
function convertApTextToCc(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string
): ValueConversionResult {
	const stringValue = Array.isArray(value) ? value.join(", ") : String(value);
	return { success: true, convertedValue: stringValue };
}

/**
 * Convert AP NUMERICAL to CC number
 */
function convertApNumericalToCc(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string
): ValueConversionResult {
	if (ccField.type !== "number") {
		// Convert to string for non-number CC fields
		return { success: true, convertedValue: String(value) };
	}

	const numValue = Number(value);
	if (isNaN(numValue)) {
		return {
			success: false,
			convertedValue: null,
			error: `Cannot convert "${value}" to number`
		};
	}

	return { success: true, convertedValue: numValue };
}

/**
 * Convert AP DATE to CC date
 */
function convertApDateToCc(
	value: string | number | boolean | string[] | null,
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string
): ValueConversionResult {
	const stringValue = String(value);
	
	// Basic date validation - accept ISO format or common formats
	const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
	if (!dateRegex.test(stringValue)) {
		return {
			success: false,
			convertedValue: null,
			error: `Invalid date format: ${stringValue}. Expected YYYY-MM-DD format.`
		};
	}

	return { success: true, convertedValue: stringValue };
}
