/**
 * CliniCore Value Conversion Functions
 *
 * Provides value conversion utilities for transforming CliniCore field values
 * to AutoPatient format. Handles type-specific conversions, multi-value fields,
 * and special cases like boolean to radio conversions.
 *
 * @fileoverview CC → AP value conversion functions
 * @version 1.0.0
 * @since 2024-07-28
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@/type";
import type { ValueConversionResult } from "../types";
import { getCcToApMapping } from "../fieldTypeMapping";
import { logDebug, logWarn } from "@/utils/logger";

/**
 * Convert CliniCore field value to AutoPatient format
 *
 * Main entry point for CC → AP value conversion. Handles all supported
 * field type conversions with proper error handling and logging.
 *
 * @param value - CC field value to convert
 * @param ccField - Source CC field definition
 * @param apField - Target AP field definition
 * @param requestId - Request ID for tracing
 * @returns Conversion result with success status and converted value
 */
export async function convertCcValueToAp(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string
): Promise<ValueConversionResult> {
	// Handle null/undefined values
	if (value === null || value === undefined) {
		logDebug("Converting null/undefined CC value", {
			requestId,
			ccFieldId: ccField.id,
			apFieldId: apField.id,
		});
		return { success: true, convertedValue: null };
	}

	try {
		// Get field type mapping
		const mapping = getCcToApMapping(ccField.type, ccField.allowMultipleValues);
		if (!mapping) {
			return {
				success: false,
				convertedValue: null,
				error: `No mapping found for CC field type: ${ccField.type}`
			};
		}

		// Handle specific conversion cases
		switch (ccField.type) {
			case "boolean":
				return convertCcBooleanToAp(value, ccField, apField, requestId);
			
			case "select":
				return convertCcSelectToAp(value, ccField, apField, requestId);
			
			case "select-or-custom":
				return convertCcSelectOrCustomToAp(value, ccField, apField, requestId);
			
			case "text":
				return convertCcTextToAp(value, ccField, apField, requestId);
			
			case "textarea":
				return convertCcTextareaToAp(value, ccField, apField, requestId);
			
			case "number":
				return convertCcNumberToAp(value, ccField, apField, requestId);
			
			case "telephone":
				return convertCcTelephoneToAp(value, ccField, apField, requestId);
			
			case "email":
				return convertCcEmailToAp(value, ccField, apField, requestId);
			
			case "date":
				return convertCcDateToAp(value, ccField, apField, requestId);
			
			// Special CC types - fallback to text
			case "medication":
			case "permanent-diagnoses":
			case "patient-has-recommended":
				return convertCcSpecialToAp(value, ccField, apField, requestId);
			
			default:
				return {
					success: false,
					convertedValue: null,
					error: `Unsupported CC field type: ${ccField.type}`
				};
		}
	} catch (error) {
		logWarn("Error converting CC value to AP", {
			requestId,
			ccFieldId: ccField.id,
			apFieldId: apField.id,
			error: error instanceof Error ? error.message : String(error)
		});

		return {
			success: false,
			convertedValue: null,
			error: `Conversion error: ${error instanceof Error ? error.message : String(error)}`
		};
	}
}

/**
 * Convert CC boolean to AP RADIO field
 */
function convertCcBooleanToAp(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string
): ValueConversionResult {
	if (apField.dataType !== "RADIO") {
		return {
			success: false,
			convertedValue: null,
			error: `Cannot convert CC boolean to AP ${apField.dataType}`
		};
	}

	const boolValue = Boolean(value);
	
	// Use standardized Yes/No options for boolean conversion
	// This ensures reversible transformation
	const convertedValue = boolValue ? "Yes" : "No";

	return { success: true, convertedValue };
}

/**
 * Convert CC select to AP options field
 */
function convertCcSelectToAp(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string
): ValueConversionResult {
	if (ccField.allowMultipleValues) {
		return convertCcMultiSelectToAp(value, ccField, apField, requestId);
	} else {
		return convertCcSingleSelectToAp(value, ccField, apField, requestId);
	}
}

/**
 * Convert CC multi-select to AP MULTIPLE_OPTIONS
 */
function convertCcMultiSelectToAp(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string
): ValueConversionResult {
	if (!["MULTIPLE_OPTIONS", "TEXTBOX_LIST", "CHECKBOX"].includes(apField.dataType)) {
		return {
			success: false,
			convertedValue: null,
			error: `Cannot convert CC multi-select to AP ${apField.dataType}`
		};
	}

	// Handle array values
	let values: string[];
	if (Array.isArray(value)) {
		values = value.map(v => String(v));
	} else if (typeof value === "string") {
		// Split comma-separated values if needed
		values = value.includes(",") ? value.split(",").map(v => v.trim()) : [value];
	} else {
		values = [String(value)];
	}

	// For TEXTBOX_LIST, join with newlines
	if (apField.dataType === "TEXTBOX_LIST") {
		return { success: true, convertedValue: values.join("\n") };
	}

	// For MULTIPLE_OPTIONS and CHECKBOX, return as array or comma-separated string
	return { success: true, convertedValue: values };
}

/**
 * Convert CC single-select to AP SINGLE_OPTIONS or RADIO
 */
function convertCcSingleSelectToAp(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string
): ValueConversionResult {
	if (!["SINGLE_OPTIONS", "RADIO", "TEXT"].includes(apField.dataType)) {
		return {
			success: false,
			convertedValue: null,
			error: `Cannot convert CC single-select to AP ${apField.dataType}`
		};
	}

	const stringValue = Array.isArray(value) ? value[0] : String(value);
	return { success: true, convertedValue: stringValue };
}

/**
 * Convert CC select-or-custom to AP SINGLE_OPTIONS
 */
function convertCcSelectOrCustomToAp(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string
): ValueConversionResult {
	if (apField.dataType !== "SINGLE_OPTIONS") {
		return {
			success: false,
			convertedValue: null,
			error: `Cannot convert CC select-or-custom to AP ${apField.dataType}`
		};
	}

	const stringValue = Array.isArray(value) ? value[0] : String(value);
	return { success: true, convertedValue: stringValue };
}

/**
 * Convert CC text to AP field
 */
function convertCcTextToAp(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string
): ValueConversionResult {
	if (ccField.allowMultipleValues && apField.dataType === "TEXTBOX_LIST") {
		// Handle multi-value text as TEXTBOX_LIST
		let values: string[];
		if (Array.isArray(value)) {
			values = value.map(v => String(v));
		} else {
			values = [String(value)];
		}
		return { success: true, convertedValue: values.join("\n") };
	}

	// Single value text conversion
	const stringValue = Array.isArray(value) ? value.join(", ") : String(value);
	return { success: true, convertedValue: stringValue };
}

/**
 * Convert CC textarea to AP LARGE_TEXT or TEXTBOX_LIST
 */
function convertCcTextareaToAp(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string
): ValueConversionResult {
	if (ccField.allowMultipleValues && apField.dataType === "TEXTBOX_LIST") {
		// Handle multi-value textarea as TEXTBOX_LIST
		let values: string[];
		if (Array.isArray(value)) {
			values = value.map(v => String(v));
		} else {
			values = [String(value)];
		}
		return { success: true, convertedValue: values.join("\n") };
	}

	// Single value textarea conversion
	const stringValue = Array.isArray(value) ? value.join("\n") : String(value);
	return { success: true, convertedValue: stringValue };
}

/**
 * Convert CC number to AP NUMERICAL or TEXTBOX_LIST
 */
function convertCcNumberToAp(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string
): ValueConversionResult {
	if (ccField.allowMultipleValues && apField.dataType === "TEXTBOX_LIST") {
		// Handle multi-value numbers as TEXTBOX_LIST
		let values: string[];
		if (Array.isArray(value)) {
			values = value.map(v => String(v));
		} else {
			values = [String(value)];
		}
		return { success: true, convertedValue: values.join("\n") };
	}

	// Single number conversion
	if (apField.dataType === "NUMERICAL") {
		const numValue = Number(value);
		if (isNaN(numValue)) {
			return {
				success: false,
				convertedValue: null,
				error: `Cannot convert "${value}" to number`
			};
		}
		return { success: true, convertedValue: numValue };
	}

	// Convert to text for non-numerical AP fields
	const stringValue = Array.isArray(value) ? value.join(", ") : String(value);
	return { success: true, convertedValue: stringValue };
}

/**
 * Convert CC telephone to AP PHONE or TEXTBOX_LIST
 */
function convertCcTelephoneToAp(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string
): ValueConversionResult {
	if (ccField.allowMultipleValues && apField.dataType === "TEXTBOX_LIST") {
		// Handle multi-value telephone as TEXTBOX_LIST
		let values: string[];
		if (Array.isArray(value)) {
			values = value.map(v => String(v));
		} else {
			values = [String(value)];
		}
		return { success: true, convertedValue: values.join("\n") };
	}

	// Single telephone conversion
	const stringValue = Array.isArray(value) ? value[0] : String(value);
	return { success: true, convertedValue: stringValue };
}

/**
 * Convert CC email to AP EMAIL or TEXTBOX_LIST
 */
function convertCcEmailToAp(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string
): ValueConversionResult {
	if (ccField.allowMultipleValues && apField.dataType === "TEXTBOX_LIST") {
		// Handle multi-value email as TEXTBOX_LIST
		let values: string[];
		if (Array.isArray(value)) {
			values = value.map(v => String(v));
		} else {
			values = [String(value)];
		}
		return { success: true, convertedValue: values.join("\n") };
	}

	// Single email conversion
	const stringValue = Array.isArray(value) ? value[0] : String(value);
	return { success: true, convertedValue: stringValue };
}

/**
 * Convert CC date to AP DATE
 */
function convertCcDateToAp(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string
): ValueConversionResult {
	if (apField.dataType !== "DATE") {
		// Convert to text for non-date AP fields
		const stringValue = Array.isArray(value) ? value[0] : String(value);
		return { success: true, convertedValue: stringValue };
	}

	const stringValue = Array.isArray(value) ? value[0] : String(value);
	
	// Basic date validation
	const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
	if (!dateRegex.test(stringValue)) {
		return {
			success: false,
			convertedValue: null,
			error: `Invalid date format: ${stringValue}. Expected YYYY-MM-DD format.`
		};
	}

	return { success: true, convertedValue: stringValue };
}

/**
 * Convert special CC field types to AP TEXT (fallback)
 */
function convertCcSpecialToAp(
	value: string | number | boolean | string[] | null,
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string
): ValueConversionResult {
	// Convert special CC types to text
	const stringValue = Array.isArray(value) ? value.join(", ") : String(value);
	
	logDebug("Converting special CC field type to AP TEXT", {
		requestId,
		ccFieldType: ccField.type,
		apFieldType: apField.dataType,
		originalValue: value,
		convertedValue: stringValue
	});

	return { 
		success: true, 
		convertedValue: stringValue,
		warnings: [`Special CC field type ${ccField.type} converted to text`]
	};
}
