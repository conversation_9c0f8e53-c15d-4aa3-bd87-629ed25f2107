/**
 * Custom Fields Creation Operations
 *
 * Provides field creation functionality for custom field synchronization
 * between AutoPatient (AP) and CliniCore (CC) platforms. Handles bidirectional
 * field creation with proper error handling, logging, and conflict detection.
 *
 * @fileoverview Field creation utilities for custom field synchronization
 * @version 1.0.0
 * @since 2024-07-27
 */

import apiClient from "@apiClient";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import { apToCcCustomFieldConvert } from "./apToCcCustomFieldConvert";
import { ccToApCustomFieldConvert } from "./ccToApCustomFieldConvert";
import type { FieldCreationResult } from "./types";
import { areFieldTypesCompatible, generateUniqueFieldName } from "./conflictDetector";
import { storeMappingForCreatedFields } from "./databaseOperations";
import { shouldSkip<PERSON><PERSON>ield } from "./fieldTypeMapping";

/**
 * Handle AP field creation in CC with skip logic
 *
 * According to requirements, when syncing AP to CC, if any field is missing in CC,
 * skip that field without any noise. Only create custom fields in AP, never in CC.
 *
 * @param apField - AP field that would be created in CC
 * @param requestId - Request ID for tracing
 * @returns null (field creation skipped)
 */
export async function handleApFieldCreationInCc(
	apField: APGetCustomFieldType,
	requestId: string,
): Promise<GetCCCustomField | null> {
	// Skip FILE_UPLOAD fields entirely
	if (shouldSkipAPField(apField.dataType)) {
		logDebug("Skipping FILE_UPLOAD field creation in CC", {
			requestId,
			apFieldId: apField.id,
			apFieldName: apField.name,
			apFieldType: apField.dataType,
		});
		return null;
	}

	// According to requirements: Only create custom fields in AP, never create new fields in CC
	// When syncing AP to CC, if any field is missing in CC, skip that field without any noise
	logDebug("Skipping AP field creation in CC (per requirements)", {
		requestId,
		apFieldId: apField.id,
		apFieldName: apField.name,
		apFieldType: apField.dataType,
		reason: "Only create custom fields in AP, never in CC",
	});

	return null;
}

/**
 * Create an AP field in CC platform using field conversion
 *
 * Converts an AutoPatient custom field to CliniCore format and creates it
 * on the CC platform. Handles conversion errors, API failures, and existing
 * field conflicts with comprehensive logging and error reporting.
 *
 * The function performs the following operations:
 * 1. Converts AP field to CC format using apToCcCustomFieldConvert
 * 2. Creates the field on CC platform via API
 * 3. Logs success/failure with detailed context
 * 4. Handles existing field conflicts gracefully
 *
 * @param apField - AP field to convert and create in CC
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Promise resolving to created CC field or null if creation failed
 *
 * @throws {Error} When field conversion fails or API request encounters unexpected errors
 *
 * @example
 * ```typescript
 * const apField: APGetCustomFieldType = {
 *   id: 123,
 *   name: "Patient Notes",
 *   dataType: "TEXTAREA",
 *   required: false
 * };
 *
 * const createdField = await createApFieldInCc(apField, "req-123");
 * if (createdField) {
 *   console.log(`Created CC field: ${createdField.name} (ID: ${createdField.id})`);
 * } else {
 *   console.log("Field creation failed or field already exists");
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function createApFieldInCc(
	apField: APGetCustomFieldType,
	requestId: string,
): Promise<GetCCCustomField | null> {
	// Use the new skip logic that follows requirements
	return handleApFieldCreationInCc(apField, requestId);
}

/**
 * Create a CC field in AP platform using field conversion
 *
 * Converts a CliniCore custom field to AutoPatient format and creates it
 * on the AP platform. Handles conversion errors, API failures, and existing
 * field conflicts with comprehensive logging and error reporting.
 *
 * The function performs the following operations:
 * 1. Converts CC field to AP format using ccToApCustomFieldConvert
 * 2. Creates the field on AP platform via API
 * 3. Logs success/failure with detailed context
 * 4. Handles existing field conflicts gracefully
 *
 * @param ccField - CC field to convert and create in AP
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Promise resolving to created AP field or null if creation failed
 *
 * @throws {Error} When field conversion fails or API request encounters unexpected errors
 *
 * @example
 * ```typescript
 * const ccField: GetCCCustomField = {
 *   id: 456,
 *   name: "medical_notes",
 *   label: "Medical Notes",
 *   type: "TEXTAREA",
 *   required: false
 * };
 *
 * const createdField = await createCcFieldInAp(ccField, "req-456");
 * if (createdField) {
 *   console.log(`Created AP field: ${createdField.name} (ID: ${createdField.id})`);
 * } else {
 *   console.log("Field creation failed or field already exists");
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function createCcFieldInAp(
	ccField: GetCCCustomField,
	requestId: string,
): Promise<APGetCustomFieldType | null> {
	try {
		logDebug("Converting CC field to AP format", {
			requestId,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			ccFieldType: ccField.type,
		});

		const apFieldData = ccToApCustomFieldConvert(ccField);
		const createdField = await apiClient.ap.apCustomfield.create(apFieldData);

		logInfo("Successfully created AP field from CC field", {
			requestId,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			ccFieldType: ccField.type,
			apFieldId: createdField.id,
			apFieldName: createdField.name,
			apFieldType: createdField.dataType,
			typeConversion: `${ccField.type} → ${createdField.dataType}`,
		});

		return createdField;
	} catch (error) {
		// Check if error indicates field already exists
		const errorMessage = error instanceof Error ? error.message : String(error);
		const isFieldKeyConflict =
			errorMessage.toLowerCase().includes("fieldkey already exists") ||
			errorMessage.toLowerCase().includes("field key already exists") ||
			errorMessage.toLowerCase().includes("fieldkey is already taken") ||
			errorMessage.toLowerCase().includes("field key is already taken") ||
			errorMessage.toLowerCase().includes("duplicate fieldkey") ||
			errorMessage.toLowerCase().includes("duplicate field key");

		const isGeneralConflict =
			errorMessage.toLowerCase().includes("already exists") ||
			errorMessage.toLowerCase().includes("duplicate") ||
			errorMessage.toLowerCase().includes("conflict") ||
			errorMessage.toLowerCase().includes("name is already taken");

		if (isFieldKeyConflict) {
			// Handle fieldKey-specific conflicts with intelligent resolution
			logInfo("AP field creation failed due to fieldKey conflict, attempting resolution", {
				requestId,
				ccFieldId: ccField.id,
				ccFieldName: ccField.name,
				ccFieldType: ccField.type,
				errorMessage,
				action: "fieldkey_conflict_detected",
			});

			// Extract the fieldKey from the CC field conversion to identify the conflict
			const apFieldData = ccToApCustomFieldConvert(ccField);
			const conflictingFieldKey = apFieldData.fieldKey || `contact.${ccField.name.toLowerCase().replace(/[^a-z0-9]/g, '_')}`;

			// Attempt intelligent conflict resolution
			const resolvedField = await resolveFieldKeyConflict(ccField, conflictingFieldKey, requestId);

			if (resolvedField) {
				logInfo("Successfully resolved fieldKey conflict", {
					requestId,
					ccFieldId: ccField.id,
					ccFieldName: ccField.name,
					resolvedApFieldId: resolvedField.id,
					resolvedApFieldName: resolvedField.name,
					conflictingFieldKey,
					action: "fieldkey_conflict_resolved",
				});
				return resolvedField;
			} else {
				logWarn("Failed to resolve fieldKey conflict, field creation abandoned", {
					requestId,
					ccFieldId: ccField.id,
					ccFieldName: ccField.name,
					conflictingFieldKey,
					errorMessage,
					action: "fieldkey_conflict_resolution_failed",
				});
			}
		} else if (isGeneralConflict) {
			// Handle general field conflicts (name-based)
			logInfo("AP field creation failed due to existing field (name conflict)", {
				requestId,
				ccFieldId: ccField.id,
				ccFieldName: ccField.name,
				errorMessage,
				action: "name_conflict_detected",
			});
		} else {
			// Handle unexpected errors
			logError(`Failed to create AP field from CC field ${ccField.id}`, error);
		}
		return null;
	}
}

/**
 * Create field with comprehensive result tracking
 *
 * Enhanced field creation function that provides detailed result information
 * including success status, created field data, and error details. This
 * function wraps the basic creation functions with additional result tracking.
 *
 * @param sourceField - Source field to create on target platform
 * @param targetPlatform - Platform where the field should be created ("ap" | "cc")
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Promise resolving to comprehensive creation result
 *
 * @example
 * ```typescript
 * const result = await createFieldWithResult(apField, "cc", "req-123");
 *
 * if (result.success && result.field) {
 *   console.log(`Successfully created field: ${result.field.name}`);
 * } else {
 *   console.error(`Creation failed: ${result.error}`);
 *   if (result.existingFieldConflict) {
 *     console.log("Field already exists on target platform");
 *   }
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function createFieldWithResult(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetPlatform: "ap" | "cc",
	requestId: string,
): Promise<FieldCreationResult> {
	try {
		let createdField: APGetCustomFieldType | GetCCCustomField | null = null;

		if (targetPlatform === "cc" && "dataType" in sourceField) {
			// Creating CC field from AP field
			createdField = await createApFieldInCc(
				sourceField as APGetCustomFieldType,
				requestId,
			);
		} else if (targetPlatform === "ap" && "type" in sourceField) {
			// Creating AP field from CC field
			createdField = await createCcFieldInAp(
				sourceField as GetCCCustomField,
				requestId,
			);
		} else {
			return {
				success: false,
				error: "Invalid source field type for target platform",
			};
		}

		if (createdField) {
			return {
				success: true,
				field: createdField,
			};
		} else {
			return {
				success: false,
				error: "Field creation failed",
				existingFieldConflict: true,
			};
		}
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		const isExistingFieldConflict =
			errorMessage.toLowerCase().includes("already exists") ||
			errorMessage.toLowerCase().includes("duplicate") ||
			errorMessage.toLowerCase().includes("conflict") ||
			errorMessage.toLowerCase().includes("name is already taken");

		return {
			success: false,
			error: errorMessage,
			existingFieldConflict: isExistingFieldConflict,
		};
	}
}

/**
 * Find existing AP field by fieldKey
 *
 * Searches through all AP custom fields to find one with the specified fieldKey.
 * This is used during conflict resolution when the AP API reports a fieldKey
 * already exists error.
 *
 * @param fieldKey - The fieldKey to search for (e.g., "contact.field_name")
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Promise resolving to the existing AP field or null if not found
 *
 * @example
 * ```typescript
 * const existingField = await findApFieldByFieldKey("contact.patient_notes", "req-123");
 * if (existingField) {
 *   console.log(`Found existing field: ${existingField.name} (ID: ${existingField.id})`);
 * }
 * ```
 *
 * @since 1.0.0
 */
async function findApFieldByFieldKey(
	fieldKey: string,
	requestId: string,
): Promise<APGetCustomFieldType | null> {
	try {
		logDebug("Searching for AP field by fieldKey", {
			requestId,
			fieldKey,
		});

		// Fetch all AP fields to search through them
		const allApFields = await apiClient.ap.apCustomfield.allWithParentFilter(true); // Invalidate cache

		// Find field with matching fieldKey
		const existingField = allApFields.find(field => field.fieldKey === fieldKey);

		if (existingField) {
			logDebug("Found existing AP field with matching fieldKey", {
				requestId,
				fieldKey,
				existingFieldId: existingField.id,
				existingFieldName: existingField.name,
				existingFieldType: existingField.dataType,
			});
			return existingField;
		}

		logDebug("No AP field found with matching fieldKey", {
			requestId,
			fieldKey,
			totalFieldsSearched: allApFields.length,
		});
		return null;
	} catch (error) {
		logError(`Failed to search for AP field by fieldKey: ${fieldKey}`, error);
		return null;
	}
}

/**
 * Resolve fieldKey conflict during AP field creation
 *
 * Handles the situation where AP field creation fails due to an existing fieldKey.
 * Implements intelligent conflict resolution by:
 * 1. Finding the existing AP field with the conflicting fieldKey
 * 2. Assessing type compatibility between CC and existing AP field
 * 3. Creating mapping if compatible, or creating new field with unique fieldKey if incompatible
 *
 * @param ccField - CC field that caused the fieldKey conflict
 * @param conflictingFieldKey - The fieldKey that already exists in AP
 * @param requestId - Request ID for tracing and logging correlation
 * @returns Promise resolving to the existing AP field (if mapped) or newly created AP field (if unique), or null if resolution failed
 *
 * @example
 * ```typescript
 * // Called when AP field creation fails with fieldKey conflict
 * const resolvedField = await resolveFieldKeyConflict(
 *   ccField,
 *   "contact.patient_notes",
 *   "req-123"
 * );
 *
 * if (resolvedField) {
 *   console.log(`Conflict resolved: ${resolvedField.name} (ID: ${resolvedField.id})`);
 * }
 * ```
 *
 * @since 1.0.0
 */
async function resolveFieldKeyConflict(
	ccField: GetCCCustomField,
	conflictingFieldKey: string,
	requestId: string,
): Promise<APGetCustomFieldType | null> {
	try {
		logInfo("Starting fieldKey conflict resolution", {
			requestId,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			ccFieldType: ccField.type,
			conflictingFieldKey,
			action: "conflict_resolution_started",
		});

		// Step 1: Find the existing AP field with the conflicting fieldKey
		const existingApField = await findApFieldByFieldKey(conflictingFieldKey, requestId);

		if (!existingApField) {
			logWarn("Could not find existing AP field with conflicting fieldKey", {
				requestId,
				conflictingFieldKey,
				ccFieldId: ccField.id,
				ccFieldName: ccField.name,
				action: "conflict_resolution_failed",
				reason: "existing_field_not_found",
			});
			return null;
		}

		// Step 2: Assess field type compatibility
		const isCompatible = areFieldTypesCompatible(ccField, existingApField, "cc");

		if (isCompatible) {
			// Step 3a: Types are compatible - create mapping to existing field
			logInfo("Field types are compatible, creating mapping to existing AP field", {
				requestId,
				ccFieldId: ccField.id,
				ccFieldName: ccField.name,
				ccFieldType: ccField.type,
				existingApFieldId: existingApField.id,
				existingApFieldName: existingApField.name,
				existingApFieldType: existingApField.dataType,
				typeMapping: `${ccField.type} ↔ ${existingApField.dataType}`,
				conflictResolution: "mapped_to_existing_compatible_field",
			});

			try {
				await storeMappingForCreatedFields(existingApField, ccField, requestId);

				logInfo("Successfully resolved fieldKey conflict by mapping to existing compatible field", {
					requestId,
					ccFieldId: ccField.id,
					ccFieldName: ccField.name,
					existingApFieldId: existingApField.id,
					existingApFieldName: existingApField.name,
					conflictingFieldKey,
					resolutionStrategy: "mapping_to_existing",
				});

				return existingApField;
			} catch (mappingError) {
				logError("Failed to create mapping during conflict resolution", mappingError);
				// Continue to try unique field creation as fallback
			}
		}

		// Step 3b: Types are incompatible or mapping failed - create new field with unique fieldKey
		logWarn("Field types are incompatible or mapping failed, creating new AP field with unique fieldKey", {
			requestId,
			ccFieldId: ccField.id,
			ccFieldName: ccField.name,
			ccFieldType: ccField.type,
			existingApFieldId: existingApField.id,
			existingApFieldName: existingApField.name,
			existingApFieldType: existingApField.dataType,
			conflictResolution: "creating_unique_field",
			compatibilityStatus: isCompatible ? "compatible_but_mapping_failed" : "incompatible",
		});

		// Generate unique name and fieldKey
		const allApFields = await apiClient.ap.apCustomfield.allWithParentFilter(true);
		const uniqueName = generateUniqueFieldName(ccField.name, ccField, "cc", allApFields);
		const uniqueFieldKey = `contact.${uniqueName.toLowerCase().replace(/[^a-z0-9]/g, '_')}`;

		// Create new AP field with unique name and fieldKey
		const apFieldData = ccToApCustomFieldConvert(ccField, uniqueName, uniqueFieldKey);
		const createdApField = await apiClient.ap.apCustomfield.create(apFieldData);

		if (createdApField) {
			// Store mapping for the newly created field
			await storeMappingForCreatedFields(createdApField, ccField, requestId);

			logInfo("Successfully resolved fieldKey conflict by creating unique AP field", {
				requestId,
				ccFieldId: ccField.id,
				ccFieldName: ccField.name,
				ccFieldType: ccField.type,
				newApFieldId: createdApField.id,
				newApFieldName: createdApField.name,
				newApFieldType: createdApField.dataType,
				uniqueName,
				uniqueFieldKey,
				originalConflictingFieldKey: conflictingFieldKey,
				originalConflictingFieldId: existingApField.id,
				originalConflictingFieldName: existingApField.name,
				resolutionStrategy: "created_unique_field",
				typeConversion: `${ccField.type} → ${createdApField.dataType}`,
			});

			return createdApField;
		}

		logError("Failed to create unique AP field during conflict resolution", {
			requestId,
			ccFieldId: ccField.id,
			uniqueName,
			uniqueFieldKey,
		});
		return null;

	} catch (error) {
		logError("Field conflict resolution failed with unexpected error", error);
		return null;
	}
}
