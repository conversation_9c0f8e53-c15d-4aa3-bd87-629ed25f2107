/**
 * Field Mapping Constants
 *
 * Centralized constants for field mapping between AutoPatient and CliniCore platforms.
 * Contains field type mappings, compatibility rules, and special handling configurations
 * as specified in the requirements.
 *
 * @fileoverview Centralized field mapping constants and configurations
 * @version 1.0.0
 * @since 2024-07-28
 */

import type { APFieldDataType, CCFieldType } from "./types";

/**
 * Field types that should be skipped entirely during synchronization
 */
export const SKIPPED_FIELD_TYPES: APFieldDataType[] = ["FILE_UPLOAD"];

/**
 * Boolean detection patterns for RADIO field conversion
 */
export const BOOLEAN_PATTERNS = {
	YES: ["yes", "ja", "true", "1", "on", "enabled", "accept", "agree"],
	NO: ["no", "nein", "false", "0", "off", "disabled", "decline", "disagree"],
} as const;

/**
 * Standard boolean options for CC boolean to AP RADIO conversion
 */
export const STANDARD_BOOLEAN_OPTIONS = {
	YES: "Yes",
	NO: "No",
} as const;

/**
 * Field type compatibility matrix for special cases
 * These are considered compatible even if not in the main mapping tables
 */
export const SPECIAL_COMPATIBILITY_RULES = [
	// PHONE/telephone compatibility
	{ apType: "PHONE" as APFieldDataType, ccType: "telephone" as CCFieldType },
	{ apType: "TEXT" as APFieldDataType, ccType: "telephone" as CCFieldType },
	
	// TEXT/textarea compatibility  
	{ apType: "TEXT" as APFieldDataType, ccType: "textarea" as CCFieldType },
	{ apType: "LARGE_TEXT" as APFieldDataType, ccType: "text" as CCFieldType },
	
	// Boolean/radio compatibility
	{ apType: "RADIO" as APFieldDataType, ccType: "boolean" as CCFieldType },
	
	// Multi-value text compatibility with TEXTBOX_LIST
	{ apType: "TEXTBOX_LIST" as APFieldDataType, ccType: "text" as CCFieldType, requiresMultiValue: true },
	{ apType: "TEXTBOX_LIST" as APFieldDataType, ccType: "textarea" as CCFieldType, requiresMultiValue: true },
	{ apType: "TEXTBOX_LIST" as APFieldDataType, ccType: "email" as CCFieldType, requiresMultiValue: true },
	{ apType: "TEXTBOX_LIST" as APFieldDataType, ccType: "telephone" as CCFieldType, requiresMultiValue: true },
	{ apType: "TEXTBOX_LIST" as APFieldDataType, ccType: "number" as CCFieldType, requiresMultiValue: true },
] as const;

/**
 * CC field types that are compatible with AP TEXTBOX_LIST when allowMultipleValues is true
 */
export const TEXTBOX_LIST_COMPATIBLE_CC_TYPES: CCFieldType[] = [
	"text",
	"number", 
	"textarea",
	"telephone",
	"email"
];

/**
 * CC field types that are considered text-based for conversion purposes
 */
export const TEXT_BASED_CC_TYPES: CCFieldType[] = [
	"text",
	"textarea",
	"email",
	"telephone"
];

/**
 * AP field types that are considered text-based for conversion purposes
 */
export const TEXT_BASED_AP_TYPES: APFieldDataType[] = [
	"TEXT",
	"LARGE_TEXT",
	"EMAIL",
	"PHONE",
	"MONETORY"
];

/**
 * Field name normalization patterns for German umlauts and special characters
 */
export const NORMALIZATION_PATTERNS = [
	{ from: /ä/g, to: "ae" },
	{ from: /ö/g, to: "oe" },
	{ from: /ü/g, to: "ue" },
	{ from: /ß/g, to: "ss" },
	{ from: /Ä/g, to: "AE" },
	{ from: /Ö/g, to: "OE" },
	{ from: /Ü/g, to: "UE" },
	{ from: /[^a-zA-Z0-9]/g, to: "" }, // Remove all non-alphanumeric characters
] as const;

/**
 * Common field name variations that should be considered equivalent
 */
export const FIELD_NAME_ALIASES = {
	phone: ["phone", "telephone", "tel", "phonenumber", "phone_number", "mobile", "phonemobile", "phone_mobile"],
	email: ["email", "e_mail", "e-mail", "emailaddress", "email_address", "mail"],
	address: ["address", "addr", "street", "streetaddress", "street_address"],
	name: ["name", "fullname", "full_name", "patientname", "patient_name"],
	notes: ["notes", "note", "comments", "comment", "remarks", "remark"],
	allergies: ["allergies", "allergy", "allergien", "allergie"],
	medications: ["medications", "medication", "medikamente", "medikament", "drugs", "drug"],
} as const;

/**
 * Field key extraction patterns for AutoPatient fieldKey property
 */
export const FIELD_KEY_PATTERNS = [
	/^contact\.(.+)$/,  // contact.fieldname
	/^patient\.(.+)$/,  // patient.fieldname
	/^custom\.(.+)$/,   // custom.fieldname
] as const;

/**
 * Maximum similarity threshold for field name matching
 */
export const SIMILARITY_THRESHOLDS = {
	HIGH: 0.9,      // Very similar names
	MEDIUM: 0.8,    // Moderately similar names
	LOW: 0.6,       // Loosely similar names
	MINIMUM: 0.5,   // Minimum acceptable similarity
} as const;

/**
 * Field creation conflict resolution strategies
 */
export const CONFLICT_RESOLUTION_STRATEGIES = {
	SKIP: "skip",                    // Skip field creation
	MODIFY_NAME: "modify_name",      // Modify field name to avoid conflict
	MAP_EXISTING: "map_existing",    // Map to existing compatible field
	FORCE_CREATE: "force_create",    // Force creation with unique name
} as const;

/**
 * Value conversion separators for multi-value fields
 */
export const VALUE_SEPARATORS = {
	COMMA: ",",
	NEWLINE: "\n",
	SEMICOLON: ";",
	PIPE: "|",
} as const;

/**
 * Date format patterns for validation
 */
export const DATE_PATTERNS = {
	ISO: /^\d{4}-\d{2}-\d{2}$/,                    // YYYY-MM-DD
	EUROPEAN: /^\d{2}\.\d{2}\.\d{4}$/,             // DD.MM.YYYY
	US: /^\d{2}\/\d{2}\/\d{4}$/,                   // MM/DD/YYYY
	GERMAN: /^\d{1,2}\.\d{1,2}\.\d{4}$/,           // D.M.YYYY or DD.MM.YYYY
} as const;

/**
 * Numeric validation patterns
 */
export const NUMERIC_PATTERNS = {
	INTEGER: /^-?\d+$/,
	DECIMAL: /^-?\d+(\.\d+)?$/,
	CURRENCY: /^-?\d+(\.\d{2})?$/,
	PERCENTAGE: /^-?\d+(\.\d+)?%?$/,
} as const;

/**
 * Email validation pattern
 */
export const EMAIL_PATTERN = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

/**
 * Phone number validation patterns
 */
export const PHONE_PATTERNS = {
	INTERNATIONAL: /^\+\d{1,3}\s?\d{1,14}$/,
	GERMAN: /^(\+49|0)\d{2,4}\s?\d{6,8}$/,
	US: /^(\+1|1)?\s?\(?\d{3}\)?\s?\d{3}-?\d{4}$/,
	GENERIC: /^[\+\d\s\-\(\)]{7,20}$/,
} as const;

/**
 * Field validation rules by type
 */
export const VALIDATION_RULES = {
	EMAIL: {
		pattern: EMAIL_PATTERN,
		maxLength: 254,
		required: false,
	},
	PHONE: {
		patterns: Object.values(PHONE_PATTERNS),
		maxLength: 20,
		required: false,
	},
	TEXT: {
		maxLength: 255,
		required: false,
	},
	LARGE_TEXT: {
		maxLength: 65535,
		required: false,
	},
	NUMERICAL: {
		pattern: NUMERIC_PATTERNS.DECIMAL,
		min: Number.MIN_SAFE_INTEGER,
		max: Number.MAX_SAFE_INTEGER,
		required: false,
	},
	DATE: {
		patterns: Object.values(DATE_PATTERNS),
		required: false,
	},
} as const;

/**
 * Logging levels for field processing
 */
export const LOG_LEVELS = {
	DEBUG: "DEBUG",
	INFO: "INFO", 
	WARN: "WARN",
	ERROR: "ERROR",
} as const;

/**
 * Field synchronization directions
 */
export const SYNC_DIRECTIONS = {
	AP_TO_CC: "ap_to_cc",
	CC_TO_AP: "cc_to_ap",
	BIDIRECTIONAL: "bidirectional",
} as const;

/**
 * Field processing actions
 */
export const FIELD_ACTIONS = {
	CREATED: "created",
	MATCHED: "matched", 
	SKIPPED: "skipped",
	FAILED: "failed",
	UPDATED: "updated",
	DELETED: "deleted",
} as const;

/**
 * Error codes for field processing
 */
export const ERROR_CODES = {
	FIELD_NOT_FOUND: "FIELD_NOT_FOUND",
	TYPE_MISMATCH: "TYPE_MISMATCH",
	VALIDATION_FAILED: "VALIDATION_FAILED",
	API_ERROR: "API_ERROR",
	CONVERSION_ERROR: "CONVERSION_ERROR",
	CONFLICT_ERROR: "CONFLICT_ERROR",
	PERMISSION_ERROR: "PERMISSION_ERROR",
} as const;

/**
 * Success codes for field processing
 */
export const SUCCESS_CODES = {
	FIELD_CREATED: "FIELD_CREATED",
	FIELD_MATCHED: "FIELD_MATCHED",
	FIELD_UPDATED: "FIELD_UPDATED",
	FIELD_SYNCHRONIZED: "FIELD_SYNCHRONIZED",
	MAPPING_STORED: "MAPPING_STORED",
} as const;
