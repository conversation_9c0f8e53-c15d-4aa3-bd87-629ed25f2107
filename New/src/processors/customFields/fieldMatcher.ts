/**
 * Field Matching System
 *
 * Intelligent field matching between AutoPatient and CliniCore custom fields
 * using normalization, similarity scoring, and conflict detection.
 *
 * @fileoverview Custom field matching and conflict resolution
 * @version 1.0.0
 * @since 2024-07-28
 */

import type { APGetCustomFieldType } from "@/type/APTypes";
import type { GetCCCustomField } from "@/type/CCTypes";
import type { FieldMatchResult, CustomFieldMapping } from "./types";
import { fieldNamesMatch, calculateFieldNameSimilarity, normalizeFieldName, matchFieldNames } from "./fieldNormalizer";
import { areFieldTypesCompatible } from "./fieldTypeMapping";
import { logDebug, logInfo, logWarn } from "@/utils/logger";

/**
 * Match AP field with CC fields
 *
 * Attempts to find the best matching CC field for a given AP field using
 * intelligent name matching and type compatibility checking.
 *
 * @param apField - AutoPatient custom field to match
 * @param ccFields - Array of CliniCore custom fields to match against
 * @param existingMappings - Existing field mappings to avoid conflicts
 * @param requestId - Request ID for tracing
 * @returns Field match result with details
 */
export function matchAPFieldWithCC(
	apField: APGetCustomFieldType,
	ccFields: GetCCCustomField[],
	existingMappings: CustomFieldMapping[],
	requestId: string,
): FieldMatchResult {
	logDebug("Matching AP field with CC fields", {
		requestId,
		apFieldId: apField.id,
		apFieldName: apField.name,
		apFieldType: apField.dataType,
		ccFieldCount: ccFields.length,
	});

	// Check if AP field is already mapped
	const existingMapping = existingMappings.find(m => m.apId === apField.id);
	if (existingMapping) {
		const ccField = ccFields.find(f => f.id === existingMapping.ccId);
		if (ccField) {
			logDebug("Found existing mapping for AP field", {
				requestId,
				apFieldId: apField.id,
				ccFieldId: ccField.id,
				mappingId: existingMapping.id,
			});
			return {
				matched: true,
				apField,
				ccField,
				matchType: "exact",
			};
		}
	}

	// Find available CC fields (not already mapped)
	const mappedCCIds = new Set(existingMappings.map(m => m.ccId).filter(Boolean));
	const availableCCFields = ccFields.filter(f => !mappedCCIds.has(f.id));

	if (availableCCFields.length === 0) {
		logWarn("No available CC fields for matching", {
			requestId,
			apFieldId: apField.id,
			totalCCFields: ccFields.length,
			mappedCCFields: mappedCCIds.size,
		});
		return {
			matched: false,
			apField,
			matchType: "none",
			conflictReason: "No available CC fields",
		};
	}

	// Use enhanced field matching with multiple strategies
	for (const ccField of availableCCFields) {
		const matchResult = matchFieldNames(
			apField.name,
			apField.fieldKey,
			ccField.name,
			ccField.label,
			0.8 // similarity threshold
		);

		if (matchResult.matched) {
			// Check type compatibility
			const isCompatible = areFieldTypesCompatible(
				apField.dataType,
				ccField.type,
				ccField.allowMultipleValues,
			);

			if (isCompatible) {
				logInfo("Found compatible field match", {
					requestId,
					apFieldId: apField.id,
					ccFieldId: ccField.id,
					apFieldName: apField.name,
					ccFieldName: ccField.name,
					matchStrategy: matchResult.strategy,
					similarity: matchResult.similarity,
				});
				return {
					matched: true,
					apField,
					ccField,
					matchType: matchResult.strategy === "direct" ? "exact" : "normalized",
				};
			} else {
				logWarn("Field names match but types incompatible", {
					requestId,
					apFieldId: apField.id,
					ccFieldId: ccField.id,
					apFieldType: apField.dataType,
					ccFieldType: ccField.type,
					ccAllowMultiple: ccField.allowMultipleValues,
					matchStrategy: matchResult.strategy,
				});
			}
		}
	}

	// No compatible match found

	logDebug("No compatible match found for AP field", {
		requestId,
		apFieldId: apField.id,
		apFieldName: apField.name,
		apFieldType: apField.dataType,
		availableFieldCount: availableCCFields.length,
	});

	return {
		matched: false,
		apField,
		matchType: "none",
		conflictReason: "No compatible match found",
	};
}

/**
 * Match CC field with AP fields
 *
 * Attempts to find the best matching AP field for a given CC field.
 *
 * @param ccField - CliniCore custom field to match
 * @param apFields - Array of AutoPatient custom fields to match against
 * @param existingMappings - Existing field mappings to avoid conflicts
 * @param requestId - Request ID for tracing
 * @returns Field match result with details
 */
export function matchCCFieldWithAP(
	ccField: GetCCCustomField,
	apFields: APGetCustomFieldType[],
	existingMappings: CustomFieldMapping[],
	requestId: string,
): FieldMatchResult {
	logDebug("Matching CC field with AP fields", {
		requestId,
		ccFieldId: ccField.id,
		ccFieldName: ccField.name,
		ccFieldType: ccField.type,
		apFieldCount: apFields.length,
	});

	// Check if CC field is already mapped
	const existingMapping = existingMappings.find(m => m.ccId === ccField.id);
	if (existingMapping) {
		const apField = apFields.find(f => f.id === existingMapping.apId);
		if (apField) {
			logDebug("Found existing mapping for CC field", {
				requestId,
				ccFieldId: ccField.id,
				apFieldId: apField.id,
				mappingId: existingMapping.id,
			});
			return {
				matched: true,
				apField,
				ccField,
				matchType: "exact",
			};
		}
	}

	// Find available AP fields (not already mapped)
	const mappedAPIds = new Set(existingMappings.map(m => m.apId).filter(Boolean));
	const availableAPFields = apFields.filter(f => !mappedAPIds.has(f.id));

	if (availableAPFields.length === 0) {
		logWarn("No available AP fields for matching", {
			requestId,
			ccFieldId: ccField.id,
			totalAPFields: apFields.length,
			mappedAPFields: mappedAPIds.size,
		});
		return {
			matched: false,
			ccField,
			matchType: "none",
			conflictReason: "No available AP fields",
		};
	}

	// Use enhanced field matching with multiple strategies
	for (const apField of availableAPFields) {
		const matchResult = matchFieldNames(
			apField.name,
			apField.fieldKey,
			ccField.name,
			ccField.label,
			0.8 // similarity threshold
		);

		if (matchResult.matched) {
			// Check type compatibility
			const isCompatible = areFieldTypesCompatible(
				apField.dataType,
				ccField.type,
				ccField.allowMultipleValues,
			);

			if (isCompatible) {
				logInfo("Found compatible field match", {
					requestId,
					ccFieldId: ccField.id,
					apFieldId: apField.id,
					ccFieldName: ccField.name,
					apFieldName: apField.name,
					matchStrategy: matchResult.strategy,
					similarity: matchResult.similarity,
				});
				return {
					matched: true,
					apField,
					ccField,
					matchType: matchResult.strategy === "direct" ? "exact" : "normalized",
				};
			} else {
				logWarn("Field names match but types incompatible", {
					requestId,
					ccFieldId: ccField.id,
					apFieldId: apField.id,
					ccFieldType: ccField.type,
					apFieldType: apField.dataType,
					ccAllowMultiple: ccField.allowMultipleValues,
					matchStrategy: matchResult.strategy,
				});
			}
		}
	}

	logDebug("No compatible match found for CC field", {
		requestId,
		ccFieldId: ccField.id,
		ccFieldName: ccField.name,
		ccFieldType: ccField.type,
		availableFieldCount: availableAPFields.length,
	});

	return {
		matched: false,
		ccField,
		matchType: "none",
		conflictReason: "No compatible match found",
	};
}

/**
 * Enhanced field matching using comprehensive strategies
 *
 * Uses the new matchFieldNames function that implements multiple matching strategies:
 * 1. Direct name comparison (AP name vs CC name/label)
 * 2. Field key extraction and comparison
 * 3. Normalized name comparison (handles German umlauts, special chars)
 * 4. Similarity-based matching with configurable threshold
 *
 * This replaces the old individual matching functions with a unified approach
 * that follows the requirements for intelligent field matching.
 */

/**
 * Check if two custom fields match using comprehensive comparison
 *
 * Enhanced matching logic that compares fields by name, label, and fieldKey properties
 * using normalized string matching. Handles case differences, special characters, umlauts,
 * and provides multiple matching strategies for better field detection.
 *
 * @param apField - AutoPatient custom field object to match
 * @param ccField - CliniCore custom field object to match against
 * @returns True if fields are considered a match, false otherwise
 */
export function fieldsMatch(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
): boolean {
	const matchResult = matchFieldNames(
		apField.name,
		apField.fieldKey,
		ccField.name,
		ccField.label,
		0.8 // Use high similarity threshold for fieldsMatch
	);

	return matchResult.matched;
}

/**
 * Find existing custom field that matches the given field
 *
 * Searches through a list of fields to find one that matches using the enhanced
 * field matching logic.
 *
 * @param sourceField - Field to find a match for
 * @param targetFields - Array of fields to search in
 * @param sourcePlatform - Platform of the source field ("ap" or "cc")
 * @returns Matching field if found, undefined otherwise
 */
export function findExistingCustomField(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetFields: (APGetCustomFieldType | GetCCCustomField)[],
	sourcePlatform: "ap" | "cc"
): APGetCustomFieldType | GetCCCustomField | undefined {
	if (sourcePlatform === "ap") {
		const apField = sourceField as APGetCustomFieldType;
		const ccFields = targetFields as GetCCCustomField[];

		return ccFields.find(ccField => fieldsMatch(apField, ccField));
	} else {
		const ccField = sourceField as GetCCCustomField;
		const apFields = targetFields as APGetCustomFieldType[];

		return apFields.find(apField => fieldsMatch(apField, ccField));
	}
}

/**
 * Find matching field with enhanced logic
 *
 * Wrapper function that provides the same interface as the old findMatchingField
 * but uses the new enhanced matching logic.
 *
 * @param sourceField - Field to find a match for
 * @param targetFields - Array of fields to search in
 * @param sourcePlatform - Platform of the source field
 * @returns Matching field if found, undefined otherwise
 */
export function findMatchingField(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetFields: (APGetCustomFieldType | GetCCCustomField)[],
	sourcePlatform: "ap" | "cc"
): APGetCustomFieldType | GetCCCustomField | undefined {
	return findExistingCustomField(sourceField, targetFields, sourcePlatform);
}
