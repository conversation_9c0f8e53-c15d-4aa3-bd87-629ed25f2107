/**
 * Field Name Normalization and Matching Utilities
 *
 * Provides intelligent field name normalization and matching capabilities for
 * AutoPatient and CliniCore custom field synchronization. Handles German umlauts,
 * special characters, case differences, and various naming conventions.
 *
 * @fileoverview Field name normalization and matching utilities
 * @version 1.0.0
 * @since 2024-07-28
 */

import { logDebug } from "@/utils/logger";

/**
 * Normalize field name for comparison
 *
 * Removes special characters, German umlauts, spaces, and converts to lowercase
 * for consistent field name matching across platforms.
 *
 * @param fieldName - Field name to normalize
 * @returns Normalized field name
 *
 * @example
 * ```typescript
 * normalizeFieldName("Patienten-Allergien"); // "patientenallergien"
 * normalizeFieldName("phone_mobile"); // "phonemobile"
 * normalizeFieldName("E-Mail Adresse"); // "emailadresse"
 * ```
 */
export function normalizeFieldName(fieldName: string): string {
	return fieldName
		.toLowerCase()
		// Replace German umlauts
		.replace(/ä/g, "ae")
		.replace(/ö/g, "oe")
		.replace(/ü/g, "ue")
		.replace(/ß/g, "ss")
		// Remove special characters and spaces
		.replace(/[^a-z0-9]/g, "")
		.trim();
}

/**
 * Check if two field names match using normalization
 *
 * Compares field names after normalization to handle various naming conventions,
 * special characters, and language differences.
 *
 * @param name1 - First field name
 * @param name2 - Second field name
 * @returns True if names match after normalization
 *
 * @example
 * ```typescript
 * fieldNamesMatch("phone-mobile", "phoneMobile"); // true
 * fieldNamesMatch("E-Mail", "email"); // true
 * fieldNamesMatch("Patienten Allergien", "patientenallergien"); // true
 * ```
 */
export function fieldNamesMatch(name1: string, name2: string): boolean {
	const normalized1 = normalizeFieldName(name1);
	const normalized2 = normalizeFieldName(name2);
	return normalized1 === normalized2;
}

/**
 * Calculate similarity score between two field names
 *
 * Uses Levenshtein distance algorithm to calculate similarity between
 * normalized field names. Returns a score between 0 and 1.
 *
 * @param name1 - First field name
 * @param name2 - Second field name
 * @returns Similarity score (0-1, where 1 is identical)
 *
 * @example
 * ```typescript
 * calculateFieldNameSimilarity("phone", "phone-mobile"); // ~0.8
 * calculateFieldNameSimilarity("email", "e-mail"); // ~0.9
 * calculateFieldNameSimilarity("allergies", "allergien"); // ~0.7
 * ```
 */
export function calculateFieldNameSimilarity(name1: string, name2: string): number {
	const normalized1 = normalizeFieldName(name1);
	const normalized2 = normalizeFieldName(name2);

	if (normalized1 === normalized2) {
		return 1.0;
	}

	const distance = levenshteinDistance(normalized1, normalized2);
	const maxLength = Math.max(normalized1.length, normalized2.length);
	
	if (maxLength === 0) {
		return 1.0;
	}

	return 1 - (distance / maxLength);
}

/**
 * Extract field key from AutoPatient field
 *
 * Extracts the field key portion from AP fieldKey property for matching.
 * Handles various fieldKey formats like "contact.fieldname" or "patient.fieldname".
 *
 * @param fieldKey - AutoPatient fieldKey property
 * @returns Extracted field name or original fieldKey if no pattern matches
 *
 * @example
 * ```typescript
 * extractFieldKeyName("contact.phone_mobile"); // "phone_mobile"
 * extractFieldKeyName("patient.allergies"); // "allergies"
 * extractFieldKeyName("simple_field"); // "simple_field"
 * ```
 */
export function extractFieldKeyName(fieldKey: string): string {
	// Handle contact.fieldname or patient.fieldname patterns
	const match = fieldKey.match(/^(?:contact|patient)\.(.+)$/);
	return match ? match[1] : fieldKey;
}

/**
 * Check if field names match using multiple strategies
 *
 * Comprehensive field matching that tries multiple strategies:
 * 1. Direct name comparison
 * 2. Normalized name comparison
 * 3. Field key extraction and comparison
 * 4. Similarity-based matching with threshold
 *
 * @param apFieldName - AutoPatient field name
 * @param apFieldKey - AutoPatient field key (optional)
 * @param ccFieldName - CliniCore field name
 * @param ccFieldLabel - CliniCore field label
 * @param similarityThreshold - Minimum similarity score (default: 0.8)
 * @returns Match result with strategy used
 */
export function matchFieldNames(
	apFieldName: string,
	apFieldKey: string | undefined,
	ccFieldName: string,
	ccFieldLabel: string,
	similarityThreshold: number = 0.8,
): { matched: boolean; strategy: string; similarity?: number } {
	// Strategy 1: Direct name matching
	if (fieldNamesMatch(apFieldName, ccFieldName) || fieldNamesMatch(apFieldName, ccFieldLabel)) {
		return { matched: true, strategy: "direct" };
	}

	// Strategy 2: Field key extraction matching
	if (apFieldKey) {
		const extractedKey = extractFieldKeyName(apFieldKey);
		if (fieldNamesMatch(extractedKey, ccFieldName) || fieldNamesMatch(extractedKey, ccFieldLabel)) {
			return { matched: true, strategy: "fieldkey" };
		}
	}

	// Strategy 3: Similarity-based matching
	const nameSimilarity = calculateFieldNameSimilarity(apFieldName, ccFieldName);
	const labelSimilarity = calculateFieldNameSimilarity(apFieldName, ccFieldLabel);
	const maxSimilarity = Math.max(nameSimilarity, labelSimilarity);

	if (maxSimilarity >= similarityThreshold) {
		return { matched: true, strategy: "similarity", similarity: maxSimilarity };
	}

	// Strategy 4: Field key similarity matching
	if (apFieldKey) {
		const extractedKey = extractFieldKeyName(apFieldKey);
		const keySimilarity = Math.max(
			calculateFieldNameSimilarity(extractedKey, ccFieldName),
			calculateFieldNameSimilarity(extractedKey, ccFieldLabel)
		);

		if (keySimilarity >= similarityThreshold) {
			return { matched: true, strategy: "fieldkey-similarity", similarity: keySimilarity };
		}
	}

	return { matched: false, strategy: "none" };
}

/**
 * Calculate Levenshtein distance between two strings
 *
 * @param str1 - First string
 * @param str2 - Second string
 * @returns Levenshtein distance
 */
function levenshteinDistance(str1: string, str2: string): number {
	const matrix: number[][] = [];

	for (let i = 0; i <= str2.length; i++) {
		matrix[i] = [i];
	}

	for (let j = 0; j <= str1.length; j++) {
		matrix[0][j] = j;
	}

	for (let i = 1; i <= str2.length; i++) {
		for (let j = 1; j <= str1.length; j++) {
			if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
				matrix[i][j] = matrix[i - 1][j - 1];
			} else {
				matrix[i][j] = Math.min(
					matrix[i - 1][j - 1] + 1, // substitution
					matrix[i][j - 1] + 1,     // insertion
					matrix[i - 1][j] + 1      // deletion
				);
			}
		}
	}

	return matrix[str2.length][str1.length];
}

/**
 * Generate unique field name to avoid conflicts
 *
 * Creates a unique field name by appending a suffix when conflicts are detected.
 * Useful for creating new fields when type incompatibilities exist.
 *
 * @param baseName - Base field name
 * @param existingNames - Array of existing field names to avoid
 * @param maxAttempts - Maximum attempts to find unique name (default: 10)
 * @returns Unique field name
 *
 * @example
 * ```typescript
 * generateUniqueFieldName("phone", ["phone", "phone_2"]); // "phone_3"
 * generateUniqueFieldName("email", ["email"]); // "email_2"
 * ```
 */
export function generateUniqueFieldName(
	baseName: string,
	existingNames: string[],
	maxAttempts: number = 10,
): string {
	const normalizedExisting = existingNames.map(name => normalizeFieldName(name));
	const normalizedBase = normalizeFieldName(baseName);

	// If base name doesn't conflict, return it
	if (!normalizedExisting.includes(normalizedBase)) {
		return baseName;
	}

	// Try appending numbers
	for (let i = 2; i <= maxAttempts + 1; i++) {
		const candidate = `${baseName}_${i}`;
		const normalizedCandidate = normalizeFieldName(candidate);
		
		if (!normalizedExisting.includes(normalizedCandidate)) {
			return candidate;
		}
	}

	// Fallback with timestamp if all attempts failed
	const timestamp = Date.now().toString().slice(-6);
	return `${baseName}_${timestamp}`;
}
