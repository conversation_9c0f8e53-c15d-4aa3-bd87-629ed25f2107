/**
 * Field Type Mapping Configuration
 *
 * Comprehensive bidirectional field type mapping between AutoPatient (AP) and
 * CliniCore (CC) platforms with special handling for complex conversions.
 *
 * @fileoverview Field type mapping configuration and conversion rules
 * @version 1.0.0
 * @since 2024-07-28
 */

import type {
	APFieldDataType,
	CCFieldType,
	APToCCFieldMapping,
	CCToAPFieldMapping,
} from "./types";
import { logWarn } from "@/utils/logger";

/**
 * AutoPatient to CliniCore field type mappings
 *
 * Defines how AP field types should be converted to CC field types,
 * including special handling requirements and multi-value configurations.
 */
export const AP_TO_CC_MAPPINGS: APToCCFieldMapping[] = [
	{
		apType: "TEXT",
		ccType: "text",
		description: "Simple text field mapping",
	},
	{
		apType: "LARGE_TEXT",
		ccType: "textarea",
		description: "Large text to textarea mapping",
	},
	{
		apType: "NUMERICAL",
		ccType: "number",
		description: "Numerical field mapping",
	},
	{
		apType: "PHONE",
		ccType: "telephone",
		description: "Phone number field mapping",
	},
	{
		apType: "MONETORY",
		ccType: "text",
		description: "Monetary values stored as text in CC",
	},
	{
		apType: "CHECKBOX",
		ccType: "select",
		allowMultipleValues: true,
		description: "Checkbox to multi-select mapping",
	},
	{
		apType: "SINGLE_OPTIONS",
		ccType: "select",
		allowMultipleValues: false,
		description: "Single option to single-select mapping",
	},
	{
		apType: "MULTIPLE_OPTIONS",
		ccType: "select",
		allowMultipleValues: true,
		description: "Multiple options to multi-select mapping",
	},
	{
		apType: "DATE",
		ccType: "date",
		description: "Date field mapping",
	},
	{
		apType: "RADIO",
		ccType: "select",
		allowMultipleValues: false,
		requiresSpecialHandling: true,
		description: "Radio to select mapping with boolean detection",
	},
	{
		apType: "EMAIL",
		ccType: "email",
		description: "Email field mapping",
	},
	{
		apType: "TEXTBOX_LIST",
		ccType: "text",
		allowMultipleValues: true,
		description: "Textbox list to multi-value text mapping",
	},
	// FILE_UPLOAD is intentionally omitted - these fields are skipped entirely
];

/**
 * CliniCore to AutoPatient field type mappings
 *
 * Defines how CC field types should be converted to AP field types,
 * including fallback handling for unmapped types.
 */
export const CC_TO_AP_MAPPINGS: CCToAPFieldMapping[] = [
	{
		ccType: "text",
		apType: "TEXT",
		description: "Simple text field mapping",
	},
	{
		ccType: "text",
		allowMultipleValues: true,
		apType: "TEXTBOX_LIST",
		description: "Multi-value text to textbox list mapping",
	},
	{
		ccType: "textarea",
		apType: "LARGE_TEXT",
		description: "Textarea to large text mapping",
	},
	{
		ccType: "textarea",
		allowMultipleValues: true,
		apType: "TEXTBOX_LIST",
		description: "Multi-value textarea to textbox list mapping",
	},
	{
		ccType: "select",
		allowMultipleValues: true,
		apType: "MULTIPLE_OPTIONS",
		description: "Multi-select to multiple options mapping",
	},
	{
		ccType: "select",
		allowMultipleValues: false,
		apType: "SINGLE_OPTIONS",
		description: "Single-select to single options mapping",
	},
	{
		ccType: "boolean",
		apType: "RADIO",
		requiresSpecialHandling: true,
		description: "Boolean to radio with Yes/No options mapping",
	},
	{
		ccType: "select-or-custom",
		apType: "SINGLE_OPTIONS",
		description: "Select-or-custom to single options mapping",
	},
	{
		ccType: "number",
		apType: "NUMERICAL",
		description: "Number field mapping",
	},
	{
		ccType: "number",
		allowMultipleValues: true,
		apType: "TEXTBOX_LIST",
		description: "Multi-value number to textbox list mapping",
	},
	{
		ccType: "telephone",
		apType: "PHONE",
		description: "Telephone field mapping",
	},
	{
		ccType: "telephone",
		allowMultipleValues: true,
		apType: "TEXTBOX_LIST",
		description: "Multi-value telephone to textbox list mapping",
	},
	{
		ccType: "email",
		apType: "EMAIL",
		description: "Email field mapping",
	},
	{
		ccType: "email",
		allowMultipleValues: true,
		apType: "TEXTBOX_LIST",
		description: "Multi-value email to textbox list mapping",
	},
	{
		ccType: "date",
		apType: "DATE",
		description: "Date field mapping",
	},
	// Special CC types that don't have direct AP equivalents
	{
		ccType: "medication",
		apType: "TEXT",
		description: "Medication field mapped to text (fallback)",
	},
	{
		ccType: "permanent-diagnoses",
		apType: "TEXT",
		description: "Permanent diagnoses mapped to text (fallback)",
	},
	{
		ccType: "patient-has-recommended",
		apType: "TEXT",
		description: "Patient recommendation mapped to text (fallback)",
	},
];

/**
 * Get AP to CC field type mapping
 *
 * @param apType - AutoPatient field data type
 * @param isMultiValue - Whether the field supports multiple values
 * @returns Matching CC field mapping or undefined
 */
export function getAPToCCMapping(
	apType: APFieldDataType,
	isMultiValue?: boolean,
): APToCCFieldMapping | undefined {
	return AP_TO_CC_MAPPINGS.find((mapping) => {
		if (mapping.apType !== apType) return false;
		
		// For fields that can be multi-value, check the specific configuration
		if (mapping.allowMultipleValues !== undefined) {
			return mapping.allowMultipleValues === isMultiValue;
		}
		
		return true;
	});
}

/**
 * Get CC to AP field type mapping
 *
 * @param ccType - CliniCore field type
 * @param allowMultipleValues - Whether the CC field allows multiple values
 * @returns Matching AP field mapping or undefined
 */
export function getCCToAPMapping(
	ccType: CCFieldType,
	allowMultipleValues?: boolean,
): CCToAPFieldMapping | undefined {
	// First try to find exact match including multi-value configuration
	const exactMatch = CC_TO_AP_MAPPINGS.find((mapping) => {
		if (mapping.ccType !== ccType) return false;
		
		if (mapping.allowMultipleValues !== undefined) {
			return mapping.allowMultipleValues === allowMultipleValues;
		}
		
		return allowMultipleValues === undefined || allowMultipleValues === false;
	});
	
	if (exactMatch) return exactMatch;
	
	// Fallback to any mapping for this CC type
	return CC_TO_AP_MAPPINGS.find((mapping) => mapping.ccType === ccType);
}

/**
 * Check if a field type requires special handling during conversion
 *
 * @param apType - AutoPatient field type
 * @param ccType - CliniCore field type
 * @returns True if special handling is required
 */
export function requiresSpecialHandling(
	apType?: APFieldDataType,
	ccType?: CCFieldType,
): boolean {
	if (apType) {
		const mapping = AP_TO_CC_MAPPINGS.find((m) => m.apType === apType);
		return mapping?.requiresSpecialHandling === true;
	}
	
	if (ccType) {
		const mapping = CC_TO_AP_MAPPINGS.find((m) => m.ccType === ccType);
		return mapping?.requiresSpecialHandling === true;
	}
	
	return false;
}

/**
 * Check if AP field type should be skipped during synchronization
 *
 * @param apType - AutoPatient field data type
 * @returns True if field should be skipped
 */
export function shouldSkipAPField(apType: APFieldDataType): boolean {
	// FILE_UPLOAD fields are never synchronized
	return apType === "FILE_UPLOAD";
}

/**
 * Get all supported AP field types
 *
 * @returns Array of supported AP field types
 */
export function getSupportedAPTypes(): APFieldDataType[] {
	return AP_TO_CC_MAPPINGS.map((mapping) => mapping.apType);
}

/**
 * Get all supported CC field types
 *
 * @returns Array of supported CC field types
 */
export function getSupportedCCTypes(): CCFieldType[] {
	return [...new Set(CC_TO_AP_MAPPINGS.map((mapping) => mapping.ccType))];
}

/**
 * Check if field types are compatible for synchronization
 *
 * @param apType - AutoPatient field type
 * @param ccType - CliniCore field type
 * @param ccAllowMultipleValues - Whether CC field allows multiple values
 * @returns True if types are compatible
 */
export function areFieldTypesCompatible(
	apType: APFieldDataType,
	ccType: CCFieldType,
	ccAllowMultipleValues?: boolean,
): boolean {
	// Check AP to CC compatibility
	const apToCcMapping = getAPToCCMapping(apType);
	if (apToCcMapping?.ccType === ccType) {
		if (apToCcMapping.allowMultipleValues !== undefined) {
			return apToCcMapping.allowMultipleValues === ccAllowMultipleValues;
		}
		return true;
	}
	
	// Check CC to AP compatibility
	const ccToApMapping = getCCToAPMapping(ccType, ccAllowMultipleValues);
	if (ccToApMapping?.apType === apType) {
		return true;
	}
	
	// Special compatibility cases
	return isSpecialCompatibilityCase(apType, ccType, ccAllowMultipleValues);
}

/**
 * Check for special compatibility cases
 *
 * @param apType - AutoPatient field type
 * @param ccType - CliniCore field type
 * @param ccAllowMultipleValues - Whether CC field allows multiple values
 * @returns True if special compatibility exists
 */
function isSpecialCompatibilityCase(
	apType: APFieldDataType,
	ccType: CCFieldType,
	ccAllowMultipleValues?: boolean,
): boolean {
	// PHONE/telephone and TEXT/textarea are considered compatible
	if (
		(apType === "PHONE" && ccType === "telephone") ||
		(apType === "TEXT" && ccType === "textarea")
	) {
		return true;
	}

	// Boolean/radio compatibility for Yes/No values
	if (apType === "RADIO" && ccType === "boolean") {
		return true;
	}

	// TEXTBOX_LIST compatibility with multi-value CC fields
	if (apType === "TEXTBOX_LIST" && ccAllowMultipleValues === true) {
		const compatibleTypes: CCFieldType[] = ["text", "number", "textarea", "telephone", "email"];
		return compatibleTypes.includes(ccType);
	}

	return false;
}

/**
 * Check if AP RADIO field is a boolean candidate
 *
 * Determines if a RADIO field should be converted to CC boolean based on
 * having exactly 2 options that match Yes/No patterns.
 *
 * @param apField - AutoPatient RADIO field
 * @returns True if field should be converted to boolean
 */
export function isRadioBooleanCandidate(apField: { dataType: string; picklistOptions?: string[] }): boolean {
	if (apField.dataType !== "RADIO" || !apField.picklistOptions) {
		return false;
	}

	const options = apField.picklistOptions;
	if (options.length !== 2) {
		return false;
	}

	// Normalize options for comparison
	const normalizedOptions = options.map(opt => opt.toLowerCase().trim());

	// Check for Yes/No patterns (English and German)
	const yesPatterns = ["yes", "ja", "true", "1", "on", "enabled"];
	const noPatterns = ["no", "nein", "false", "0", "off", "disabled"];

	const hasYes = normalizedOptions.some(opt => yesPatterns.includes(opt));
	const hasNo = normalizedOptions.some(opt => noPatterns.includes(opt));

	return hasYes && hasNo;
}

/**
 * Get AP to CC field type mapping with special handling
 *
 * @param apType - AutoPatient field type
 * @param apField - Optional AP field for special handling (e.g., RADIO boolean detection)
 * @returns CC field type mapping or null if should be skipped
 */
export function getApToCcMapping(
	apType: APFieldDataType,
	apField?: { dataType: string; picklistOptions?: string[] }
): APToCCFieldMapping | null {
	// Skip FILE_UPLOAD fields entirely
	if (apType === "FILE_UPLOAD") {
		return null;
	}

	// Special handling for RADIO fields - check if it's a boolean candidate
	if (apType === "RADIO" && apField && isRadioBooleanCandidate(apField)) {
		return {
			apType: "RADIO",
			ccType: "boolean",
			requiresSpecialHandling: true,
			description: "Radio with Yes/No options to boolean mapping"
		};
	}

	return getAPToCCMapping(apType);
}

/**
 * Get CC to AP field type mapping with fallback
 *
 * @param ccType - CliniCore field type
 * @param allowMultipleValues - Whether CC field allows multiple values
 * @returns AP field type mapping
 */
export function getCcToApMapping(
	ccType: CCFieldType,
	allowMultipleValues: boolean = false
): CCToAPFieldMapping {
	const mapping = getCCToAPMapping(ccType, allowMultipleValues);

	if (mapping) {
		return mapping;
	}

	// Fallback for unmapped CC field types
	logWarn("Unmapped CC field type, using TEXT fallback", {
		ccType,
		allowMultipleValues,
		fallbackType: "TEXT"
	});

	return {
		ccType: ccType,
		allowMultipleValues,
		apType: "TEXT",
		description: `Fallback mapping for unmapped CC type: ${ccType} → TEXT`
	};
}
